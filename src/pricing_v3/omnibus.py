import logging

from datetime import (
    datetime,
    timedelta,
)
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

from custom.utils.dates import daterange
from gallery.types import FurnitureType
from pricing_v3 import registry as PricingRegistry
from pricing_v3.models import PricingVersion
from promotions.models import Promotion
from promotions.utils import strikethrough_promo
from regions.cached_region import CachedRegionData
from regions.mixins import RegionCalculationsObject
from regions.models import (
    CurrencyRate,
    RegionRate,
)
from regions.types import RegionLikeObject

logger = logging.getLogger('cstm')


class OmnibusCalculator:
    PERIOD = timedelta(days=30)

    def __init__(self, region: CachedRegionData):
        start_date, end_date = self.get_dates_for_period()
        self.end_date = end_date
        self.start_date = start_date
        self.region = region
        self.promotions = self.get_promotions()
        self.pricing_coefficients = self.get_pricing_coefficients()
        self.currency_rates = self.get_currency_rates()
        self.region_rates = self.get_region_rates()

    @classmethod
    def get_dates_for_period(cls) -> tuple[datetime.date, datetime.date]:
        """
        The lowest price that needs to be calculated using omnibus,
        shall be calculated for the last 30 days if there isn't any global promo active
        or for the last 30 days before the start of the global promo.
        """
        global_promo = strikethrough_promo()
        end_date = global_promo.start_date if global_promo else timezone.now()
        start_date = end_date - cls.PERIOD
        return start_date, end_date

    @classmethod
    def get_instance(cls, region: RegionLikeObject) -> 'OmnibusCalculator':
        region = (
            region
            if isinstance(region, CachedRegionData)
            else region.cached_region_data
        )
        instance = cache.get(f'omnibus_calculator_{region.name}')
        return instance or cls.create_and_cache_instance(region)

    @classmethod
    def create_and_cache_instance(cls, region: CachedRegionData) -> 'OmnibusCalculator':
        instance = cls(region)
        cache.set(f'omnibus_calculator_{region.name}', instance)
        return instance

    def _get_base_price(
        self,
        geometry: FurnitureType,
        region_calculations_object: RegionCalculationsObject | None = None,
    ) -> Decimal:
        if region_calculations_object:
            return Decimal(
                geometry.get_regionalized_price(
                    region=self.region,
                    region_calculations_object=region_calculations_object,
                )
            )
        return Decimal(geometry.get_regionalized_price(region=self.region))

    def calculate_lowest_price(
        self,
        geometry: FurnitureType,
        region_calculations_object: RegionCalculationsObject | None = None,
    ) -> Decimal:
        try:
            if settings.OMNIBUS_SIMPLIFIED:
                price = self._get_base_price(geometry, region_calculations_object)
            else:
                prices = self._get_prices_in_daterange(geometry)
                price = sorted(prices, key=lambda p: p['price'])[0]['price']

            return price
        except (KeyError, TypeError):
            # old cached pricing throws KeyError, let's return the base price
            # TypeError when we're lacking some data in pricing
            logger.exception(
                'Error calculating omnibus price, returning base price.',
                exc_info=True,
            )
            return self._get_base_price(geometry, region_calculations_object)

    def _get_prices_in_daterange(
        self,
        geometry: FurnitureType,
    ) -> list[dict[datetime.date, Decimal]]:
        prices = []
        for date in daterange(self.start_date, self.end_date):
            calculator = PricingRegistry.get_pricing_calculator(
                date=date,
                region=self.region,
                coefficients=self.pricing_coefficients[date],
            )
            try:
                price = calculator.get_price(geometry)
            except NotImplementedError:
                # return full price if the furniture is new
                discounted_price = self._get_base_price(geometry)
            else:
                regionalized_price = self._apply_currency_rate(
                    price=price,
                    currency_rate=self.currency_rates[date].get(
                        self.region.currency_code
                    ),
                    region_rate=self.region_rates[date].get(self.region.name),
                )
                discounted_price = self._apply_promotions(
                    regionalized_price=regionalized_price,
                    geometry=geometry,
                    promotions=self.promotions[date],
                )
            prices.append({'date': date, 'price': discounted_price})
        return prices

    def _apply_promotions(
        self,
        regionalized_price: Decimal,
        geometry: FurnitureType,
        promotions: list[Promotion],
    ) -> Decimal:
        lowest = regionalized_price
        for promotion in promotions:
            region_amount_after_voucher = Decimal(
                promotion.promo_code.check_discounted_amount(
                    total_price=regionalized_price,
                    geometry=geometry,
                    region=self.region,
                )
            )
            lowest = min(lowest, region_amount_after_voucher)
        return lowest

    def _apply_currency_rate(
        self,
        price: Decimal,
        currency_rate: CurrencyRate,
        region_rate: RegionRate,
    ) -> Decimal:
        result = price * currency_rate.rate * region_rate.rate
        return result.quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    def get_promotions(self) -> dict[datetime, list[Promotion]]:
        promos = Promotion.get_global_promotions_in_range(
            region=self.region,
            start_date=self.start_date,
            end_date=self.end_date,
        )
        if promos is None:
            promos = []
        promos_by_date = {}
        for date in daterange(self.start_date, self.end_date):
            promos_by_date[date] = [
                promo
                for promo in promos
                if promo.start_date.date() <= date <= promo.end_date.date()
            ]

        return promos_by_date

    def get_pricing_coefficients(self) -> dict[datetime.date, PricingVersion]:
        """
        Pick pricing that was in force at given time,
        preferring regional pricing if they exist.
        """
        pricing_versions = list(
            PricingVersion.objects.filter(
                created_at__date__lte=self.end_date,
            )
            .select_related('region')
            .order_by('-created_at')
        )
        coefficients = {}
        fallback = PricingVersion.objects.filter(region__isnull=True).last()

        for date in daterange(self.start_date, self.end_date):
            for pricing_version in pricing_versions:
                if (
                    getattr(pricing_version.region, 'id', None) == self.region.id
                    and pricing_version.created_at.date() <= date
                ):
                    coefficients[date] = pricing_version
                    break
                elif pricing_version.created_at.date() <= date:
                    coefficients[date] = pricing_version
                    break
            if date not in coefficients:
                coefficients[date] = fallback
        return coefficients

    def get_currency_rates(self) -> dict[datetime.date, dict[str, CurrencyRate]]:
        rates_by_date = {}
        defaults = (
            CurrencyRate.objects.order_by('currency', '-time')
            .distinct('currency')
            .select_related('currency')
        )
        currency_rates = set(
            CurrencyRate.objects.select_related('currency').filter(
                time__gte=self.start_date,
                time__lte=self.end_date,
            )
        )
        for date in daterange(self.start_date, self.end_date):
            rates_in_date = {
                rate for rate in currency_rates if rate.time.date() >= date
            }
            rates_in_date.update(set(defaults))
            rates_by_date[date] = {rate.currency.code: rate for rate in rates_in_date}

        return rates_by_date

    def get_region_rates(self) -> dict[datetime.date, dict[str, RegionRate]]:
        rates_by_date = {}
        defaults = (
            RegionRate.objects.order_by('region', '-time')
            .distinct('region')
            .select_related('region')
        )
        region_rates = RegionRate.objects.select_related('region').filter(
            time__gte=self.start_date,
            time__lte=self.end_date,
        )
        for date in daterange(self.start_date, self.end_date):
            rates_in_date = {rr for rr in region_rates if rr.time.date() >= date}
            rates_in_date.update(set(defaults))
            rates_by_date[date] = {rate.region.name: rate for rate in rates_in_date}

        return rates_by_date
